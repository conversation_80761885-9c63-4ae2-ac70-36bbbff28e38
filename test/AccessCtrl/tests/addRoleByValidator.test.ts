import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addRoleByValidator()', () => {
  const validatorId = toBytes32('x12')
  const role = '0xffeeddccbbaa99887766554433221100ffeeddccbbaa99887766554433221100'

  let sender
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
  }

  const initData = async () => {
    sender = await accounts[7]
    ;({ accessCtrl } = await contractInitialize({
      accounts,
      customAddress: { validator: await sender.getAddress() },
    }))
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態', () => {
      it('権限が付与されること', async () => {
        const tx = await accessCtrlFuncs.addRoleByValidator({
          accessCtrl,
          accounts,
          validatorId,
          role,
          account: await accounts[5].getAddress(),
          options: { sender },
        })

        const expectParams = {
          role,
          account: await accounts[5].getAddress(),
          sender: await sender.getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleGranted')
          .withArgs(...Object.values(expectParams))
        assert.ok(
          await accessCtrlFuncs.hasRole({
            accessCtrl,
            role,
            account: await accounts[5].getAddress(),
          }),
        )
      })
    })

    describe('accounts[5]に権限が付与されている状態', () => {
      it('同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと', async () => {
        const tx = await accessCtrlFuncs.addRoleByValidator({
          accessCtrl,
          accounts,
          validatorId,
          role,
          account: await accounts[5].getAddress(),
          options: { sender },
        })

        await expect(tx).to.not.emit(accessCtrl, 'RoleGranted')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態', () => {
      it('権限にAdmin権限を指定した場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addRoleByValidator({
          accessCtrl,
          accounts,
          validatorId,
          role: BASE.ROLE.ADMIN,
          account: await accounts[5].getAddress(),
          options: { sender },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addRoleByValidator({
          accessCtrl,
          accounts,
          validatorId,
          role: BASE.ROLE.ADMIN,
          account: await accounts[5].getAddress(),
          options: { sender },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
