import { Provider } from '@contracts/Provider'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { loadFixture } from '@nomicfoundation/hardhat-network-helpers'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import hre from 'hardhat'

/**
 * 呼び出し元検証をしている関数を呼び出すため、任意のaccountのアドレスを設定したコントラクトのセットアップ処理をする
 * @returns [ContractManagerInstance, AccessCtrlInstance, ProviderInstance, IssuerInstance, ValidatorInstance, AccountInstance, TokenInstance, TransferProxyInstance]
 */
export const contractInitializeFixture = async () => {
  const [admin] = await hre.ethers.getSigners()

  // Deploy storage logic
  const ProviderLib = await (await hre.ethers.getContractFactory('ProviderLib')).deploy()
  const IssuerLogicExecuteLib = await (await hre.ethers.getContractFactory('IssuerLogicExecuteLib')).deploy()
  const IssuerLogicCallLib = await (await hre.ethers.getContractFactory('IssuerLogicCallLib')).deploy()
  const ValidatorLogicExecuteLib = await (await hre.ethers.getContractFactory('ValidatorLogicExecuteLib')).deploy()
  const ValidatorLogicCallLib = await (await hre.ethers.getContractFactory('ValidatorLogicCallLib')).deploy()
  const AccountLib = await (await hre.ethers.getContractFactory('AccountLib')).deploy()
  const FinancialZoneAccountLib = await (await hre.ethers.getContractFactory('FinancialZoneAccountLib')).deploy()
  const BusinessZoneAccountLib = await (await hre.ethers.getContractFactory('BusinessZoneAccountLib')).deploy()
  const TokenLib = await (await hre.ethers.getContractFactory('TokenLib')).deploy()
  const renewableEnergyTokenLogic = await (await hre.ethers.getContractFactory('RenewableEnergyTokenLib')).deploy()

  // Deploy contract
  const remigrationLib = await (await hre.ethers.getContractFactory('RemigrationLib')).deploy()
  const accessCtrl = await (await hre.ethers.getContractFactory('AccessCtrl')).deploy()
  const account = await (
    await hre.ethers.getContractFactory('Account', {
      libraries: {
        AccountLib: await AccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const financialZoneAccount = await (
    await hre.ethers.getContractFactory('FinancialZoneAccount', {
      libraries: {
        FinancialZoneAccountLib: await FinancialZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const businessZoneAccount = await (
    await hre.ethers.getContractFactory('BusinessZoneAccount', {
      libraries: {
        BusinessZoneAccountLib: await BusinessZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const contractManager = await (await hre.ethers.getContractFactory('ContractManager')).deploy()
  const issuer = await (
    await hre.ethers.getContractFactory('IssuerLogic', {
      libraries: {
        IssuerLogicCallLib: await IssuerLogicCallLib.getAddress(),
        IssuerLogicExecuteLib: await IssuerLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const issuerStorage = await (
    await hre.ethers.getContractFactory('IssuerStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const provider = (await (
    await hre.ethers.getContractFactory('Provider', {
      libraries: {
        ProviderLib: await ProviderLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()) as unknown as Provider
  const token = await (
    await hre.ethers.getContractFactory('Token', {
      libraries: {
        TokenLib: await TokenLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const ibcToken = await (
    await hre.ethers.getContractFactory('IBCToken', {
      libraries: {
        TokenLib: await TokenLib.getAddress(),
      },
    })
  ).deploy()
  const financialCheck = await (await hre.ethers.getContractFactory('FinancialCheck')).deploy()
  const renewableEnergyToken = await (
    await hre.ethers.getContractFactory('RenewableEnergyToken', {
      libraries: {
        RenewableEnergyTokenLib: await renewableEnergyTokenLogic.getAddress(),
      },
    })
  ).deploy()
  const validator = await (
    await hre.ethers.getContractFactory('ValidatorLogic', {
      libraries: {
        ValidatorLogicCallLib: await ValidatorLogicCallLib.getAddress(),
        ValidatorLogicExecuteLib: await ValidatorLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const validatorStorage = await (
    await hre.ethers.getContractFactory('ValidatorStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()

  const transferProxy = await (await hre.ethers.getContractFactory('TransferProxy')).deploy()
  const customTransfer1 = await (await hre.ethers.getContractFactory('TransferableMock1')).deploy()
  const customTransfer2 = await (await hre.ethers.getContractFactory('TransferableMock2')).deploy()
  const customTransfer3 = await (await hre.ethers.getContractFactory('TransferableMock3')).deploy()
  const oracle = await (await hre.ethers.getContractFactory('Oracle')).deploy()
  const remigrationRestore = await (await hre.ethers.getContractFactory('RemigrationRestore')).deploy()
  const remigrationBackup = await (await hre.ethers.getContractFactory('RemigrationBackup')).deploy()

  // contract-ibc
  const ibcHandler = await (await hre.ethers.getContractFactory('IBCHandlerMock')).deploy()
  const accountSyncBridge = await (await hre.ethers.getContractFactory('AccountSyncBridge')).deploy()
  const balanceSyncBridge = await (await hre.ethers.getContractFactory('BalanceSyncBridge')).deploy()
  const jpyTokenTransferBridge = await (await hre.ethers.getContractFactory('JPYTokenTransferBridge')).deploy()
  const ibcTokenMock = await (await hre.ethers.getContractFactory('IBCTokenMock')).deploy()
  const providerMock = await (await hre.ethers.getContractFactory('ProviderMock')).deploy()
  const validatorMock = await (await hre.ethers.getContractFactory('ValidatorMock')).deploy()
  const accountMock = await (await hre.ethers.getContractFactory('AccountMock')).deploy()
  const accessCtrlMock = await (await hre.ethers.getContractFactory('AccessCtrlMock')).deploy()
  const businessZoneAccountMock = await (await hre.ethers.getContractFactory('BusinessZoneAccountMock')).deploy()

  // Initialize
  const contractManagerAddress = await contractManager.getAddress()
  const validatorStorageAddress = await validatorStorage.getAddress()
  const validatorLogicAddress = await validator.getAddress()
  const issuerStorageAddress = await issuerStorage.getAddress()
  const issuerLogicAddress = await issuer.getAddress()
  const tokenAddress = await token.getAddress()
  await accessCtrl.getAddress()
  await provider.getAddress()
  await validator.getAddress()
  await account.getAddress()
  await financialZoneAccount.getAddress()
  await businessZoneAccount.getAddress()
  await ibcToken.getAddress()
  await financialCheck.getAddress()
  await renewableEnergyToken.getAddress()
  await transferProxy.getAddress()
  await customTransfer1.getAddress()
  await customTransfer2.getAddress()
  await customTransfer3.getAddress()
  await oracle.getAddress()
  await remigrationLib.getAddress()
  await remigrationRestore.getAddress()
  await remigrationBackup.getAddress()

  const ibcHandlerAddress = await ibcHandler.getAddress()
  const ibcTokenMockAddress = await ibcTokenMock.getAddress()
  const providerMockAddress = await providerMock.getAddress()
  const validatorMockAddress = await validatorMock.getAddress()
  const accountMockAddress = await accountMock.getAddress()
  const accessCtrlMockAddress = await accessCtrlMock.getAddress()
  const businessZoneAccountMockAddress = await businessZoneAccountMock.getAddress()
  await accountSyncBridge.getAddress()
  await balanceSyncBridge.getAddress()
  await jpyTokenTransferBridge.getAddress()

  await contractManager.initialize()
  await accessCtrl.initialize(contractManagerAddress, await admin.getAddress())
  await provider.initialize(contractManagerAddress)
  await issuer.initialize(contractManagerAddress, issuerStorageAddress)
  await issuerStorage.initialize(contractManagerAddress, issuerLogicAddress)
  await validator.initialize(contractManagerAddress, validatorStorageAddress)
  await validatorStorage.initialize(contractManagerAddress, validatorLogicAddress)
  await account.initialize(contractManagerAddress)
  await financialZoneAccount.initialize(contractManagerAddress)
  await businessZoneAccount.initialize(contractManagerAddress)
  await token.initialize(contractManagerAddress)
  await ibcToken.initialize(contractManagerAddress)
  await financialCheck.initialize(contractManagerAddress)
  await renewableEnergyToken.initialize(contractManagerAddress, tokenAddress)
  await transferProxy.initialize(contractManagerAddress, tokenAddress)
  await customTransfer1.initialize(tokenAddress)
  await customTransfer2.initialize(tokenAddress)
  await customTransfer3.initialize(tokenAddress)
  await oracle.initialize()
  await remigrationRestore.initialize(contractManagerAddress)
  await remigrationBackup.initialize(contractManagerAddress)

  await accountSyncBridge.initialize(
    ibcHandlerAddress,
    providerMockAddress,
    validatorMockAddress,
    accessCtrlMockAddress,
    businessZoneAccountMockAddress,
    ibcTokenMockAddress,
  )
  await balanceSyncBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accountMockAddress, accessCtrlMockAddress)
  await jpyTokenTransferBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accessCtrlMockAddress)

  return {
    contractManager,
    accessCtrl,
    provider,
    issuer,
    validator,
    account,
    financialZoneAccount,
    businessZoneAccount,
    token,
    financialCheck,
    transferProxy,
    ibcToken,
  }
}

/**
 * 呼び出し元検証をしている関数を呼び出すため、任意のaccountのアドレスを設定したコントラクトのセットアップ処理をする
 * @param accounts
 * @param customAddress
 * @returns [ContractManagerInstance, AccessCtrlInstance, ProviderInstance, IssuerInstance, ValidatorInstance, AccountInstance, TokenInstance, TransferProxyInstance]
 */
export const contractInitialize = async ({
  accounts,
  customAddress,
}: {
  accounts: SignerWithAddress[]
  customAddress?: Partial<{
    provider: string
    issuer: string
    validator: string
    account: string
  }>
}) => {
  const {
    contractManager,
    accessCtrl,
    provider,
    issuer,
    validator,
    account,
    financialZoneAccount,
    businessZoneAccount,
    token,
    financialCheck,
    transferProxy,
    ibcToken,
  } = await loadFixture(contractInitializeFixture)

  // Initialize
  const accessCtrlAddress = await accessCtrl.getAddress()
  const providerAddress = await provider.getAddress()
  const issuerAddress = await issuer.getAddress()
  const validatorAddress = await validator.getAddress()
  const accountAddress = await account.getAddress()
  const financialZoneAccountAddress = await financialZoneAccount.getAddress()
  const businessZoneAccountAddress = await businessZoneAccount.getAddress()
  const tokenAddress = await token.getAddress()
  const ibcTokenAddress = await ibcToken.getAddress()
  const financialCheckAddress = await financialCheck.getAddress()
  const transferProxyAddress = await transferProxy.getAddress()

  await contractManagerFuncs.setContracts({
    contractManager,
    accounts,
    addresses: [
      accessCtrlAddress,
      // 呼び出し元検証をしている関数を呼び出すため任意のaccountのアドレスをセット
      customAddress?.provider ?? providerAddress,
      customAddress?.issuer ?? issuerAddress,
      customAddress?.validator ?? validatorAddress,
      customAddress?.account ?? accountAddress,
      financialZoneAccountAddress,
      businessZoneAccountAddress,
      tokenAddress,
      ibcTokenAddress,
      financialCheckAddress,
      transferProxyAddress,
    ],
  })

  return {
    accessCtrl,
  }
}
